# Video Compressor Script Usage

## Quick Start

1. **Make the script executable:**
   ```bash
   chmod +x video_compressor.sh
   ```

2. **Run the script:**
   ```bash
   ./video_compressor.sh
   ```

## What the script does:

1. **Asks for search mode:**
   - Option 1: Current directory only (faster)
   - Option 2: Recursive search in all subdirectories (thorough, default)
2. **Asks for progress display mode:**
   - Option 1: Show detailed ffmpeg progress (recommended, default)
   - Option 2: Hide progress details (faster output)
3. **Finds .mp4 files** based on selected search mode
4. **Checks file sizes** - only processes files larger than 100MB
5. **For each qualifying file:**
   - Renames `video.mp4` → `video.mp4.backup`
   - Compresses using: `ffmpeg -i "$temp_input" -c:v libx264 -crf 28 -c:a copy "$temp_output"`
   - Renames compressed file to `video.mp4`
   - **Result:** `video.mp4` (compressed) + `video.mp4.backup` (original)

## Configuration

Edit these variables in the script:
- `MIN_SIZE_MB=100` - Minimum file size to process
- `RECURSIVE_SEARCH=true` - Default search mode (can be changed interactively)
- `SHOW_FFMPEG_PROGRESS=true` - Default progress display mode (can be changed interactively)

## Example Output

```
🎬 Video Compression Script
==========================
🔧 Search Mode Selection:
1) Current directory only (faster)
2) Recursive search in all subdirectories (thorough)

Choose search mode (1 or 2) [default: 2]: 2
✓ Selected: Recursive search in all subdirectories

📊 FFmpeg Progress Display:
1) Show detailed progress (recommended)
2) Hide progress details (faster output)

Choose progress mode (1 or 2) [default: 1]: 1
✓ Selected: Show detailed progress

Configuration:
  - Minimum file size: 100MB
  - Recursive search: true
  - Show ffmpeg progress: true

✓ ffmpeg is available

🔍 Searching for .mp4 files recursively in current directory and all subdirectories...
📁 This will scan all folders and subfolders for video files
📏 Minimum file size: 100MB
----------------------------------------
📹 Processing: ./videos/movie.mp4 (250MB)
  → Compressing video...
  → Running: ffmpeg -i "./videos/movie.mp4" -c:v libx264 -crf 28 -c:a copy "./videos/movie_temp_compressed.mp4" -y
  → Please wait, this may take several minutes depending on file size...

  → Progress: 00:01:23 | FPS: 45.2 | Bitrate: 1024kbits/s
  → Compression completed successfully!
  → Original file backed up as: ./videos/movie.mp4.backup
  → Compressed file renamed to: ./videos/movie.mp4
  → Original: 250MB → Compressed: 180MB
  → Space saved: 70MB (28%)
  ✓ SUCCESS: ./videos/movie.mp4 processed

=========================================
SUMMARY:
Files found: 1
Files processed: 1
Files skipped: 0
Files failed: 0
=========================================
🎉 Script execution completed!
```

## Requirements

- Linux system
- ffmpeg installed (`sudo apt install ffmpeg` on Ubuntu/Debian)

## File Management

After running the script:
- **Original files:** `filename.mp4.backup`
- **Compressed files:** `filename.mp4`
- **Small files (<100MB):** Unchanged
