#!/bin/bash

# Simple Video Compression Script
# Compresses .mp4 files larger than 100MB using ffmpeg
# Usage: ./video_compressor.sh

# Configuration
MIN_SIZE_MB=100
RECURSIVE_SEARCH=true  # Set to true for recursive search in subdirectories
SHOW_FFMPEG_PROGRESS=true  # Set to false to hide ffmpeg progress details

# Function to convert bytes to MB
bytes_to_mb() {
    echo $(( $1 / 1024 / 1024 ))
}

# Function to check if ffmpeg is installed
check_ffmpeg() {
    if ! command -v ffmpeg &> /dev/null; then
        echo "ERROR: ffmpeg is not installed. Please install ffmpeg first."
        echo "Ubuntu/Debian: sudo apt install ffmpeg"
        echo "CentOS/RHEL: sudo yum install ffmpeg"
        exit 1
    fi
    echo "✓ ffmpeg is available"
}

# Function to compress a single video file
compress_video() {
    local input_file="$1"
    local file_size=$(stat -c%s "$input_file" 2>/dev/null)
    local file_size_mb=$(bytes_to_mb "$file_size")

    echo "📹 Processing: $input_file (${file_size_mb}MB)"

    # Check if file is larger than minimum size
    if [ "$file_size_mb" -lt "$MIN_SIZE_MB" ]; then
        echo "  → Skipping: File size (${file_size_mb}MB) is less than ${MIN_SIZE_MB}MB"
        return 0
    fi

    # Generate temporary output filename
    local temp_output="${input_file%.mp4}_temp_compressed.mp4"
    local backup_file="${input_file}.backup"

    echo "  → Compressing video..."
    echo "  → Running: ffmpeg -i \"$input_file\" -c:v libx264 -crf 28 -c:a copy \"$temp_output\" -y"

    if [ "$SHOW_FFMPEG_PROGRESS" = true ]; then
        echo "  → Please wait, this may take several minutes depending on file size..."
        echo ""

        # Run ffmpeg compression with progress display
        if ffmpeg -i "$input_file" -c:v libx264 -crf 28 -c:a copy "$temp_output" -y -progress pipe:1 2>&1 | while IFS= read -r line; do
            case "$line" in
                frame=*)
                    frame_num=$(echo "$line" | cut -d'=' -f2)
                    printf "\r  → Processing frame: %s" "$frame_num"
                    ;;
                fps=*)
                    fps=$(echo "$line" | cut -d'=' -f2)
                    ;;
                bitrate=*)
                    bitrate=$(echo "$line" | cut -d'=' -f2)
                    ;;
                total_size=*)
                    size=$(echo "$line" | cut -d'=' -f2)
                    ;;
                out_time_ms=*)
                    time_ms=$(echo "$line" | cut -d'=' -f2)
                    if [ "$time_ms" != "N/A" ] && [ -n "$time_ms" ]; then
                        time_sec=$((time_ms / 1000000))
                        hours=$((time_sec / 3600))
                        minutes=$(((time_sec % 3600) / 60))
                        seconds=$((time_sec % 60))
                        printf "\r  → Progress: %02d:%02d:%02d | FPS: %s | Bitrate: %s     " "$hours" "$minutes" "$seconds" "$fps" "$bitrate"
                    fi
                    ;;
                progress=end)
                    echo ""
                    echo "  → Compression completed successfully!"
                    break
                    ;;
            esac
        done; then
            echo ""
        else
            echo ""
            echo "  → ERROR: ffmpeg compression failed"
            rm -f "$temp_output"
            return 1
        fi
    else
        echo "  → Running compression (progress hidden)..."
        # Run ffmpeg compression without progress display
        if ffmpeg -i "$input_file" -c:v libx264 -crf 28 -c:a copy "$temp_output" -y 2>/dev/null; then
            echo "  → Compression completed successfully!"
        else
            echo "  → ERROR: ffmpeg compression failed"
            rm -f "$temp_output"
            return 1
        fi
    fi

    # Step 1: Rename original file to backup
    if mv "$input_file" "$backup_file"; then
        echo "  → Original file backed up as: $backup_file"
    else
        echo "  → ERROR: Failed to backup original file"
        rm -f "$temp_output"
        return 1
    fi

    # Step 2: Rename compressed file to original name
    if mv "$temp_output" "$input_file"; then
        echo "  → Compressed file renamed to: $input_file"

        # Show compression results
        local new_size=$(stat -c%s "$input_file" 2>/dev/null)
        local new_size_mb=$(bytes_to_mb "$new_size")
        local saved_mb=$((file_size_mb - new_size_mb))
        local saved_percent=$(( (saved_mb * 100) / file_size_mb ))

        echo "  → Original: ${file_size_mb}MB → Compressed: ${new_size_mb}MB"
        echo "  → Space saved: ${saved_mb}MB (${saved_percent}%)"
        echo "  ✓ SUCCESS: $input_file processed"
    else
        echo "  → ERROR: Failed to rename compressed file"
        # Restore original file
        mv "$backup_file" "$input_file"
        rm -f "$temp_output"
        return 1
    fi

    echo ""
}

# Function to find and process video files
process_videos() {
    local search_dir="."
    local find_options="-maxdepth 1"

    if [ "$RECURSIVE_SEARCH" = true ]; then
        find_options=""
        echo "🔍 Searching for .mp4 files recursively in current directory and all subdirectories..."
        echo "📁 This will scan all folders and subfolders for video files"
    else
        echo "🔍 Searching for .mp4 files in current directory only..."
        echo "📁 Subdirectories will be ignored"
    fi

    echo "📏 Minimum file size: ${MIN_SIZE_MB}MB"
    echo "----------------------------------------"

    local file_count=0
    local processed_count=0
    local skipped_count=0
    local failed_count=0

    # Find all .mp4 files
    while IFS= read -r -d '' file; do
        ((file_count++))

        if compress_video "$file"; then
            # Check if file was actually processed (not skipped)
            if [ -f "${file}.backup" ]; then
                ((processed_count++))
            else
                ((skipped_count++))
            fi
        else
            ((failed_count++))
        fi

    done < <(find "$search_dir" $find_options -name "*.mp4" -type f -print0 2>/dev/null)

    echo "========================================="
    echo "SUMMARY:"
    echo "Files found: $file_count"
    echo "Files processed: $processed_count"
    echo "Files skipped: $skipped_count"
    echo "Files failed: $failed_count"
    echo "========================================="
}

# Function to ask user for search mode
ask_search_mode() {
    echo "🔧 Search Mode Selection:"
    echo "1) Current directory only (faster)"
    echo "2) Recursive search in all subdirectories (thorough)"
    echo ""
    read -p "Choose search mode (1 or 2) [default: 2]: " choice

    case $choice in
        1)
            RECURSIVE_SEARCH=false
            echo "✓ Selected: Current directory only"
            ;;
        2|"")
            RECURSIVE_SEARCH=true
            echo "✓ Selected: Recursive search in all subdirectories"
            ;;
        *)
            echo "Invalid choice. Using default: Recursive search"
            RECURSIVE_SEARCH=true
            ;;
    esac
    echo ""
}

# Function to ask user for ffmpeg progress display
ask_progress_mode() {
    echo "📊 FFmpeg Progress Display:"
    echo "1) Show detailed progress (recommended)"
    echo "2) Hide progress details (faster output)"
    echo ""
    read -p "Choose progress mode (1 or 2) [default: 1]: " choice

    case $choice in
        1|"")
            SHOW_FFMPEG_PROGRESS=true
            echo "✓ Selected: Show detailed progress"
            ;;
        2)
            SHOW_FFMPEG_PROGRESS=false
            echo "✓ Selected: Hide progress details"
            ;;
        *)
            echo "Invalid choice. Using default: Show detailed progress"
            SHOW_FFMPEG_PROGRESS=true
            ;;
    esac
    echo ""
}

# Main script execution
main() {
    echo "🎬 Video Compression Script"
    echo "=========================="

    # Ask user for search mode
    ask_search_mode

    # Ask user for progress display mode
    ask_progress_mode

    echo "Configuration:"
    echo "  - Minimum file size: ${MIN_SIZE_MB}MB"
    echo "  - Recursive search: $RECURSIVE_SEARCH"
    echo "  - Show ffmpeg progress: $SHOW_FFMPEG_PROGRESS"
    echo "  - ffmpeg command: ffmpeg -i \"\$temp_input\" -c:v libx264 -crf 28 -c:a copy \"\$temp_output\""
    echo ""

    # Check dependencies
    check_ffmpeg
    echo ""

    # Process videos
    process_videos

    echo "🎉 Script execution completed!"
}

# Run main function
main
