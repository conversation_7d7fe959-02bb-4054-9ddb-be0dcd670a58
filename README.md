# Video Compression Script

Script bash để nén video .mp4 tự động trên Linux sử dụng ffmpeg.

## Yêu cầu hệ thống

- Linux OS
- ffmpeg
- bc (calculator)

### Cài đặt dependencies:

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install ffmpeg bc
```

**CentOS/RHEL:**
```bash
sudo yum install ffmpeg bc
```

## Cách sử dụng

### 1. Cấp quyền thực thi:
```bash
chmod +x compress_videos.sh
```

### 2. Chạy script:

**Nén video trong thư mục hiện tại:**
```bash
./compress_videos.sh
```

**Nén video trong thư mục cụ thể:**
```bash
./compress_videos.sh /path/to/video/directory
```

## Cấu hình

Bạn có thể điều chỉnh các tham số trong script:

```bash
MIN_SIZE_MB=100          # Chỉ nén file > 100MB
CRF_VALUE=28            # Chất lượng nén (18-28 tốt, 23 mặc định)
RECURSIVE=true          # Tìm kiếm trong thư mục con
BACKUP_ORIGINALS=true   # Backup file gốc
OUTPUT_SUFFIX="_compressed"  # Hậu tố file tạm
```

## Tính năng

- ✅ Tự động tìm tất cả file .mp4
- ✅ Kiểm tra dung lượng file (chỉ nén file > 100MB)
- ✅ Backup file gốc trước khi nén
- ✅ Kiểm tra dung lượng đĩa trống
- ✅ Hiển thị tiến trình và thống kê
- ✅ Xử lý lỗi và rollback nếu cần
- ✅ Kiểm tra hiệu quả nén (chỉ giữ nếu tiết kiệm > 5%)

## Lệnh ffmpeg được sử dụng

```bash
ffmpeg -i "$temp_input" -c:v libx264 -crf 28 -c:a copy "$temp_output"
```

- `-c:v libx264`: Codec video H.264
- `-crf 28`: Constant Rate Factor (chất lượng)
- `-c:a copy`: Copy audio không nén lại

## Ví dụ output

```
[INFO] Video Compression Script Started
[INFO] Configuration:
[INFO]   - Minimum file size: 100MB
[INFO]   - CRF value: 28
[INFO]   - Recursive search: true
[INFO]   - Backup originals: true
[SUCCESS] ffmpeg is installed
[INFO] Processing: video1.mp4 (250.50 MB)
[INFO] Compressing: video1.mp4 -> video1_compressed.mp4
[SUCCESS] Compression completed!
[INFO] Original size: 250.50 MB
[INFO] Compressed size: 180.25 MB
[INFO] Space saved: 28.05%
[SUCCESS] Successfully compressed: video1.mp4
```

## Lưu ý

- File gốc sẽ được backup với extension `.backup`
- Nếu nén không hiệu quả (< 5% tiết kiệm), file gốc sẽ được giữ nguyên
- Script sẽ hỏi xác nhận nếu file đã nén tồn tại
- Kiểm tra dung lượng đĩa trước khi nén để tránh hết dung lượng
