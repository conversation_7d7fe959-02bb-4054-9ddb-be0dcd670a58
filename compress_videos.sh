#!/bin/bash

# Video Compression Script
# Compresses .mp4 files larger than 100MB using ffmpeg
# Usage: ./compress_videos.sh [directory]

# Configuration
MIN_SIZE_MB=100
CRF_VALUE=28
RECURSIVE=true  # Set to false for current directory only
BACKUP_ORIGINALS=true  # Set to false to skip backup
OUTPUT_SUFFIX="_compressed"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to convert bytes to MB
bytes_to_mb() {
    echo "scale=2; $1 / 1024 / 1024" | bc -l
}

# Function to check if ffmpeg is installed
check_ffmpeg() {
    if ! command -v ffmpeg &> /dev/null; then
        print_error "ffmpeg is not installed. Please install ffmpeg first."
        print_info "On Ubuntu/Debian: sudo apt update && sudo apt install ffmpeg"
        print_info "On CentOS/RHEL: sudo yum install ffmpeg"
        exit 1
    fi
    print_success "ffmpeg is installed"
}

# Function to check available disk space
check_disk_space() {
    local file_path="$1"
    local file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path" 2>/dev/null)
    local available_space=$(df "$(dirname "$file_path")" | awk 'NR==2 {print $4}')
    
    # Convert available space from KB to bytes (df output is in KB on most systems)
    available_space=$((available_space * 1024))
    
    if [ "$file_size" -gt "$available_space" ]; then
        print_error "Not enough disk space to compress $file_path"
        return 1
    fi
    return 0
}

# Function to compress a single video file
compress_video() {
    local input_file="$1"
    local file_size=$(stat -f%z "$input_file" 2>/dev/null || stat -c%s "$input_file" 2>/dev/null)
    local file_size_mb=$(bytes_to_mb "$file_size")
    
    print_info "Processing: $input_file ($(printf "%.2f" "$file_size_mb") MB)"
    
    # Check if file is larger than minimum size
    if (( $(echo "$file_size_mb < $MIN_SIZE_MB" | bc -l) )); then
        print_warning "Skipping $input_file - size ($(printf "%.2f" "$file_size_mb") MB) is less than ${MIN_SIZE_MB}MB"
        return 0
    fi
    
    # Check disk space
    if ! check_disk_space "$input_file"; then
        return 1
    fi
    
    # Generate output filename
    local dir=$(dirname "$input_file")
    local filename=$(basename "$input_file" .mp4)
    local temp_output="${dir}/${filename}${OUTPUT_SUFFIX}.mp4"
    
    # Check if compressed file already exists
    if [ -f "$temp_output" ]; then
        print_warning "Compressed file already exists: $temp_output"
        read -p "Overwrite? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Skipping $input_file"
            return 0
        fi
    fi
    
    print_info "Compressing: $input_file -> $temp_output"
    
    # Run ffmpeg compression
    if ffmpeg -i "$input_file" -c:v libx264 -crf "$CRF_VALUE" -c:a copy "$temp_output" -y 2>/dev/null; then
        # Check if compression was successful and beneficial
        local new_size=$(stat -f%z "$temp_output" 2>/dev/null || stat -c%s "$temp_output" 2>/dev/null)
        local new_size_mb=$(bytes_to_mb "$new_size")
        local compression_ratio=$(echo "scale=2; (($file_size - $new_size) * 100) / $file_size" | bc -l)
        
        print_success "Compression completed!"
        print_info "Original size: $(printf "%.2f" "$file_size_mb") MB"
        print_info "Compressed size: $(printf "%.2f" "$new_size_mb") MB"
        print_info "Space saved: $(printf "%.2f" "$compression_ratio")%"
        
        # Check if compression was beneficial (at least 5% reduction)
        if (( $(echo "$compression_ratio < 5" | bc -l) )); then
            print_warning "Compression ratio is less than 5%. Keeping original file."
            rm "$temp_output"
            return 0
        fi
        
        # Backup original if requested
        if [ "$BACKUP_ORIGINALS" = true ]; then
            local backup_file="${input_file}.backup"
            if mv "$input_file" "$backup_file"; then
                print_info "Original file backed up as: $backup_file"
            else
                print_error "Failed to backup original file"
                rm "$temp_output"
                return 1
            fi
        else
            rm "$input_file"
        fi
        
        # Rename compressed file to original name
        if mv "$temp_output" "$input_file"; then
            print_success "Successfully compressed: $input_file"
        else
            print_error "Failed to rename compressed file"
            return 1
        fi
        
    else
        print_error "Failed to compress: $input_file"
        # Clean up failed output file
        [ -f "$temp_output" ] && rm "$temp_output"
        return 1
    fi
}

# Function to find and process video files
process_videos() {
    local search_dir="${1:-.}"
    local find_cmd="find \"$search_dir\" -name \"*.mp4\" -type f"
    
    if [ "$RECURSIVE" = false ]; then
        find_cmd="find \"$search_dir\" -maxdepth 1 -name \"*.mp4\" -type f"
    fi
    
    print_info "Searching for .mp4 files in: $search_dir"
    print_info "Recursive search: $RECURSIVE"
    print_info "Minimum file size: ${MIN_SIZE_MB}MB"
    
    local file_count=0
    local processed_count=0
    local failed_count=0
    
    while IFS= read -r -d '' file; do
        ((file_count++))
        if compress_video "$file"; then
            ((processed_count++))
        else
            ((failed_count++))
        fi
        echo "----------------------------------------"
    done < <(eval "$find_cmd" -print0)
    
    print_info "Processing complete!"
    print_info "Files found: $file_count"
    print_info "Files processed: $processed_count"
    print_info "Files failed: $failed_count"
}

# Main script execution
main() {
    print_info "Video Compression Script Started"
    print_info "Configuration:"
    print_info "  - Minimum file size: ${MIN_SIZE_MB}MB"
    print_info "  - CRF value: $CRF_VALUE"
    print_info "  - Recursive search: $RECURSIVE"
    print_info "  - Backup originals: $BACKUP_ORIGINALS"
    print_info "  - Output suffix: $OUTPUT_SUFFIX"
    echo "----------------------------------------"
    
    # Check dependencies
    check_ffmpeg
    
    # Check if bc is installed (for calculations)
    if ! command -v bc &> /dev/null; then
        print_error "bc (calculator) is not installed. Please install bc first."
        print_info "On Ubuntu/Debian: sudo apt install bc"
        exit 1
    fi
    
    # Process videos
    process_videos "$1"
    
    print_success "Script execution completed!"
}

# Run main function with all arguments
main "$@"
